
"use client";

import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { useTheme } from 'next-themes';
// import mermaid from 'mermaid'; // Removed static import
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { ExternalLink, Maximize, Minimize, Loader2, WandSparkles } from 'lucide-react';
import { fixMermaidSyntax, type MermaidFixResult } from '@/lib/mermaid-utils';

interface MermaidPreviewProps {
  initialScript?: string;
  onScriptChange: (script: string) => void;
  title?: string;
  description?: string;
  onSvgChange?: (svg: string) => void;
}

// Define a minimal type for the Mermaid API functions we use
interface MinimalMermaidAPI {
  initialize: (config: any) => void;
  render: (id: string, text: string) => Promise<{ svg: string; bindFunctions?: (element: Element) => void }>;
}

// REMOVED: Top-level initialization block
// if (typeof window !== 'undefined') {
//   mermaid.initialize({ /* ... */ });
// }

export const MermaidPreview: FC<MermaidPreviewProps> = ({
  initialScript = '',
  onScriptChange,
  onSvgChange,
  title = "Architecture Diagram Preview",
  description= "Edit the Mermaid script below to visualize your solution architecture."
}) => {
  const [script, setScript] = useState(initialScript);
  const [svg, setSvg] = useState('');
  const [error, setError] = useState('');
  const [fixMessages, setFixMessages] = useState<string[]>([]);
  const [fixError, setFixError] = useState<string>('');
  const [isFullScreen, setIsFullScreen] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);
  const [mermaid, setMermaid] = useState<MinimalMermaidAPI | null>(null);
  const [isLoadingMermaid, setIsLoadingMermaid] = useState(true);

  useEffect(() => {
    setScript(initialScript);
  }, [initialScript]);

  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Set mounted to true when component mounts
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Load and initialize Mermaid on client side
  useEffect(() => {
    async function loadAndInitializeMermaid() {
      if (typeof window === 'undefined' || !mounted) return;
      
      setIsLoadingMermaid(true);
      try {
        // Dynamically import mermaid
        const mermaidModule = (await import('mermaid')).default;
        const isDark = resolvedTheme === 'dark';
        
        // Initialize with theme-specific settings
        mermaidModule.initialize({
          startOnLoad: false,
          theme: isDark ? 'dark' : 'neutral',
          securityLevel: 'loose',
          fontFamily: 'var(--font-sans)',
          darkMode: isDark,
          themeCSS: `
            .node rect, .node circle, .node ellipse, .node polygon, .node path {
              fill: ${isDark ? '#2d3748' : '#ffffff'};
              stroke: ${isDark ? '#718096' : '#333333'};
              stroke-width: 2px;
            }
            .node .label {
              color: ${isDark ? '#e2e8f0' : '#1a202c'};
              font-family: var(--font-sans);
            }
            .edgePath .path {
              stroke: ${isDark ? '#a0aec0' : '#4a5568'};
            }
            .cluster rect {
              fill: ${isDark ? '#1e293b' : '#f1f5f9'};
              stroke: ${isDark ? '#475569' : '#cbd5e1'};
              stroke-width: 1.5px;
              rx: 4px;
              ry: 4px;
            }
            .cluster-label {
              font-family: var(--font-sans);
              font-weight: 600;
              fill: ${isDark ? '#e2e8f0' : '#1a202c'};
            }
            .label {
              font-family: var(--font-sans);
            }
          `,
        });
        
        setMermaid(mermaidModule);
        setError('');
      } catch (e: any) {
        console.error("Failed to load or initialize Mermaid:", e);
        setError("Failed to load Mermaid library. " + (e.message || ''));
        setMermaid(null);
      } finally {
        setIsLoadingMermaid(false);
      }
    }
    
    loadAndInitializeMermaid();
    
    // Cleanup function to prevent memory leaks
    return () => {
      setMermaid(null);
    };
  }, [resolvedTheme, mounted]); // Re-run when theme changes or component mounts

  // Track the current theme to detect changes
  const prevThemeRef = useRef(resolvedTheme);
  const [renderKey, setRenderKey] = useState(0);

  // Force re-render when theme changes
  useEffect(() => {
    if (prevThemeRef.current !== resolvedTheme) {
      prevThemeRef.current = resolvedTheme;
      setRenderKey(prev => prev + 1);
    }
  }, [resolvedTheme]);

  useEffect(() => {
    const renderDiagram = async () => {
      if (!mermaid) {
        // Mermaid module is not loaded yet or failed to load.
        if (!isLoadingMermaid && !error) setError("Mermaid library not available.");
        return;
      }
      
      if (script.trim() === '') {
        setSvg('');
        setError('');
        if (onSvgChange) onSvgChange('');
        return;
      }

      const containerId = `mermaid-diagram-${Date.now()}`;
      let isMounted = true;
      
      try {
        // Clear previous diagram
        setSvg('');
        
        // Add a small delay to allow the DOM to update
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // Render the diagram
        const { svg: renderedSvg } = await mermaid.render(containerId, script);
        
        if (isMounted) {
          setSvg(renderedSvg);
          setError('');
          if (onSvgChange) onSvgChange(renderedSvg);
        }
      } catch (e: any) {
        if (isMounted) {
          console.error("Failed to render Mermaid diagram:", e);
          setError(e.message || 'Invalid Mermaid syntax');
          setSvg('');
          if (onSvgChange) onSvgChange('');
        }
      }
      
      return () => {
        isMounted = false;
      };
    };

    if (!isLoadingMermaid && mermaid) {
      const timerId = setTimeout(renderDiagram, 100);
      return () => {
        clearTimeout(timerId);
      };
    }
  }, [script, mermaid, isLoadingMermaid, error, renderKey]); // Add renderKey to dependencies

  const handleScriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newScript = e.target.value;
    setScript(newScript);
    onScriptChange(newScript);
  };

  const openInMermaidLive = () => {
    // For btoa to work with unicode characters, we need to escape them first
    const preparedScript = unescape(encodeURIComponent(script));
    const encodedScript = btoa(preparedScript);
    window.open(`https://mermaid.live/edit#pako:${encodedScript}`, '_blank');
  };

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleFixSyntax = () => {
    const result: MermaidFixResult = fixMermaidSyntax(script);
    setFixMessages(result.changes || []);

    if (!result.isValid || result.error) {
      setFixError(result.error || 'Syntax could not be fully validated after fixing.');
    } else {
      setFixError('');
    }

    if (result.fixedScript !== script) {
      setScript(result.fixedScript);
      onScriptChange(result.fixedScript);
      // Clear general rendering errors if fix was applied
      setError('');
    }
  };

  const FullScreenWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    if (!isFullScreen) return <>{children}</>;
    return (
      <div className="fixed inset-0 z-[100] bg-background/90 backdrop-blur-sm p-4 overflow-auto">
        <div className="relative bg-card rounded-lg shadow-2xl p-4 max-w-full max-h-full overflow-auto">
          {children}
        </div>
      </div>
    );
  };

  // Render the diagram container with proper theming
  const renderDiagramContainer = () => (
    <div className="relative w-full h-full min-h-[300px] bg-card rounded-md border overflow-hidden transition-colors">
      {isLoadingMermaid ? (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : error && !fixError ? (
        <div className="p-4 text-destructive text-sm">
          <p className="font-medium">Render Error:</p>
          <pre className="mt-2 p-2 bg-destructive/10 rounded overflow-auto max-h-40 text-xs">
            {error}
          </pre>
        </div>
      ) : svg ? (
        <div 
          className="w-full h-full overflow-auto p-4 flex items-center justify-center"
          dangerouslySetInnerHTML={{ __html: svg }}
        />
      ) : (
        <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
          <p>Enter Mermaid syntax to see the preview</p>
        </div>
      )}
    </div>
  );

  return (
    <FullScreenWrapper>
      <Card className={`${isFullScreen ? "h-full flex flex-col" : ""} transition-colors`}>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            {description && (
              <CardDescription className="text-sm">{description}</CardDescription>
            )}
          </div>
          <div className="flex items-center space-x-1">
            {error && !fixError && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleFixSyntax}
                title="Attempt to fix syntax errors"
                className="text-yellow-600 hover:bg-yellow-50 dark:text-yellow-400 dark:hover:bg-yellow-900/20"
              >
                <WandSparkles className="h-4 w-4 mr-1" />
                <span className="text-xs">Fix</span>
              </Button>
            )}
            {fixError && (
              <p className="mt-2 text-xs text-red-500">Error during fix: {fixError}</p>
            )}
            {fixMessages.length > 0 && (
              <div className="mt-2 text-xs text-muted-foreground">
                <p className="font-semibold">Changes applied:</p>
                <ul className="list-disc list-inside">
                  {fixMessages.map((msg, index) => (
                    <li key={index}>{msg}</li>
                  ))}
                </ul>
              </div>
            )}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={openInMermaidLive} 
              title="Open in Mermaid.live" 
              disabled={!mounted || !script.trim()}
              className="text-muted-foreground hover:text-foreground"
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              <span className="text-xs">Open Editor</span>
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={toggleFullScreen} 
              title={isFullScreen ? "Exit full screen" : "Full screen"}
              className="text-muted-foreground hover:text-foreground"
            >
              {isFullScreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
          </div>
        </CardHeader>
        <CardContent className={isFullScreen ? "flex-grow grid grid-rows-2 gap-4 md:grid-rows-1 md:grid-cols-2" : "grid grid-cols-1 md:grid-cols-2 gap-4"}>
          <div className="space-y-2 h-full flex flex-col">
            <div className="flex items-center justify-between">
              <label htmlFor="mermaid-script" className="text-sm font-medium">
                Mermaid Syntax
              </label>
              <div className="text-xs text-muted-foreground">
                {script.length} characters
              </div>
            </div>
            <div className="relative flex-1 min-h-[300px]">
              <Textarea
                id="mermaid-script"
                value={script}
                onChange={handleScriptChange}
                placeholder="graph TD\n  A[Start] --> B[Process]\n  B --> C[End]"
                className="h-full min-h-[300px] font-mono text-sm resize-none"
                spellCheck="false"
              />
              {!script.trim() && (
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="text-muted-foreground text-sm p-4 text-center">
                    <p>Enter Mermaid syntax to see the preview</p>
                    <p className="text-xs mt-2 opacity-75">
                      Example: graph TD; A[Start] --&gt; B[Process]; B --&gt; C[End]
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="space-y-2 h-full flex flex-col">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Preview</span>
              <div className="text-xs text-muted-foreground">
                {svg ? 'Rendered' : 'No diagram'}
              </div>
            </div>
            {renderDiagramContainer()}
          </div>
        </CardContent>
      </Card>
    </FullScreenWrapper>
  );
};

export default MermaidPreview;
