---
trigger: always_on
---

Please adapt the globs depending on your project structure.

---
name: nextjs-best-practices.mdc
description: Best practices for Next.js applications and routing
globs: **/*.{ts,tsx}
---

- Use the App Router for better performance and organization of routes.
- Implement proper error boundaries to handle errors gracefully.
- Optimize data fetching with `getServerSideProps` and `getStaticProps` as needed.
- Utilize dynamic routing for cleaner URL structures.
- Leverage Next.js Image component for optimized image loading.

---
name: react-best-practices.mdc
description: Best practices for React applications
globs: **/*.{ts,tsx,js,jsx}
---

- Use functional components and hooks for state management.
- Keep components small and focused on a single responsibility.
- Use PropTypes or TypeScript for type checking props.
- Implement context for global state management where necessary.
- Optimize performance with React.memo and useCallback.

---
name: typescript-best-practices.mdc 
description: TypeScript coding standards and type safety guidelines
globs: **/*.{ts,tsx}
---

- Use strict null checks to avoid runtime errors.
- Prefer interfaces over type aliases for object shapes.
- Utilize type guards and assertions for better type safety.
- Implement proper type inference to reduce redundancy.
- Keep types organized in separate files for maintainability.

---
name: react-hook-form-best-practices.mdc
description: Best practices for using React Hook Form
globs: **/*.{ts,tsx}
---

- Use `useForm` hook to manage form state efficiently.
- Leverage `Controller` for integrating with UI libraries.
- Implement validation using `resolver` for better error handling.
- Use `watch` to monitor form field changes dynamically.
- Keep form components reusable and composable.

---
name: tailwindcss-best-practices.mdc
description: Best practices for styling with Tailwind CSS
globs: **/*.{ts,tsx,css}
---

- Use utility-first classes for rapid styling and layout.
- Create custom themes using Tailwind's configuration.
- Utilize `@apply` for reusable styles in CSS files.
- Ensure responsive design by using Tailwind's responsive utilities.
- Optimize for performance by purging unused styles in production.

---
name: zod-best-practices.mdc
description: Best practices for schema validation with Zod
globs: **/*.{ts,tsx}
---

- Define schemas for data validation to ensure type safety.
- Use `refine` for custom validation logic.
- Leverage `merge` to combine multiple schemas for complex data structures.
- Implement error handling to provide user-friendly feedback.
- Keep schemas organized and modular for maintainability.

---
name: jest-best-practices.mdc
description: Best practices for testing with Jest
globs: **/*.{ts,tsx}
---

- Write unit tests for individual components and functions.
- Use `describe` and `it` blocks for better test organization.
- Mock external dependencies to isolate tests.
- Implement coverage reports to identify untested code.
- Use `beforeEach` and `afterEach` for setup and teardown logic.