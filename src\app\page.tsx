"use client";

import { useState, useEffect } from 'react';
import type React from 'react';
import { AppHeader } from '@/components/AppHeader';
import { MermaidPreview } from '@/components/MermaidPreview';
import { Button, buttonVariants } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from '@/hooks/use-toast';
import type { AnalyzeRequirementsInput } from '@/ai/flows/requirements-analyzer';
import { ISuggestion } from '@/types/proposal';

interface IUserPreferences {
  preferredTechnologies?: string[];
  technicalConstraints?: string[];
  budgetConsiderations?: string;
  timelineConstraints?: string;
  complianceRequirements?: string[];
  integrationRequirements?: string[];
  scalabilityRequirements?: string;
  securityRequirements?: string[];
  additionalConsiderations?: string;
}
import { analyzeRequirementsAction, generateSolutionOverviewAction, enhanceProposalAction, generateArchitectureDiagramAction, generateComprehensiveProposalAction } from './actions';
import { Loader2, FileText, Lightbulb, KeyRound, Combine, Network, Sparkles, Download, AlertTriangle, UploadCloud, FileUp, CircleX, Zap, Clock, CheckCircle } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { logServerError } from '@/lib/error-utils';
import { ProposalAmendmentSection } from '@/components/proposal/ProposalAmendmentSection';

const initialMermaidScript = `graph TD;
    A[Client's Core Problem] -->|Identified Via RFP| B(Proposed Solution);
    B --> C{Feature 1: Requirement Analysis};
    B --> D{Feature 2: Solution Synthesis};
    C --> E[Key Benefit A];
    D --> F[Key Benefit B];
    B --> G((OEM Integration Suggestion));
    G -.-> H[OEM Partner X];
    G -.-> I[OEM Partner Y];`;

const SectionCard: React.FC<{
  title: string;
  description: string;
  icon: React.ElementType;
  children: React.ReactNode;
  actionButton?: React.ReactNode;
  isLoading?: boolean;
}> = ({ title, description, icon: Icon, children, actionButton, isLoading }) => (
  <Card className="shadow-lg">
    <CardHeader>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Icon className="w-7 h-7 text-primary" />
          <div>
            <CardTitle className="text-xl">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
        </div>
        {isLoading && <Loader2 className="h-5 w-5 animate-spin text-primary" />}
      </div>
    </CardHeader>
    <CardContent className="space-y-4">
      {children}
    </CardContent>
    {actionButton && (
      <CardFooter>
        {actionButton}
      </CardFooter>
    )}
  </Card>
);


// Helper function to build the draft proposal markdown
const buildDraftProposalMarkdown = (
  understandingReqs: string,
  proposalText: string,
  oemSolutions: string[],
  technicalImplementation: string,
  diagramSection: string,
  appliedSuggestions: ISuggestion[],
  enhancementSuggestions: string[]
): string => {
  return `
# Proposal Draft

## Understanding the Requirements
${understandingReqs || "No content generated yet."}

## Solution Overview
${proposalText || "No content generated yet."}

### Suggested OEM Solutions
${oemSolutions.length > 0 ? oemSolutions.map(s => `- ${s}`).join('\n') : "No OEM solutions suggested yet."}

${technicalImplementation ? `## Technical Implementation\n\n${technicalImplementation}\n\n` : ''}
${diagramSection}

## Applied Suggestions
${appliedSuggestions.length > 0 
  ? appliedSuggestions.map((s, i) => `${i + 1}. ${s.text}`).join('\n\n') 
  : "No suggestions have been applied yet."}

## Enhancement Suggestions (Internal Use)
${enhancementSuggestions.length > 0 
  ? enhancementSuggestions.map(s => `- ${s}`).join('\n') 
  : "No enhancement suggestions generated yet."}
  `.trim();
};

export default function ProposalPilotPage() {
  const [rfpContent, setRfpContent] = useState('');
  const [rfpPdfDataUri, setRfpPdfDataUri] = useState<string | null>(null);
  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [understandingReqs, setUnderstandingReqs] = useState('');
  const [solutionKeyPoints, setSolutionKeyPoints] = useState('');
  const [solutionOverview, setSolutionOverview] = useState('');
  const [oemSolutions, setOemSolutions] = useState<string[]>([]);
  const [mermaidScript, setMermaidScript] = useState(initialMermaidScript);
  const [mermaidSvg, setMermaidSvg] = useState('');
  const [enhancementSuggestions, setEnhancementSuggestions] = useState<string[]>([]);

  const [isLoadingRequirements, setIsLoadingRequirements] = useState(false);
  const [isLoadingSolution, setIsLoadingSolution] = useState(false);
  const [isLoadingEnhancements, setIsLoadingEnhancements] = useState(false);

  const [understandingReqsAttempted, setUnderstandingReqsAttempted] = useState(false);
  const [solutionOverviewAttempted, setSolutionOverviewAttempted] = useState(false);

  // New state for architecture generation
  const [architectureInput, setArchitectureInput] = useState('');
  const [diagramType, setDiagramType] = useState<'conceptual' | 'reference'>('conceptual');
  const [isLoadingDiagram, setIsLoadingDiagram] = useState(false);

  // New state for comprehensive proposal generation
  const [isLoadingComprehensive, setIsLoadingComprehensive] = useState(false);
  const [comprehensiveProgress, setComprehensiveProgress] = useState(0);
  const [comprehensiveCurrentStep, setComprehensiveCurrentStep] = useState<string>('');
  const [comprehensiveProposal, setComprehensiveProposal] = useState('');
  const [currentProposalText, setCurrentProposalText] = useState('');
  const [technicalImplementation, setTechnicalImplementation] = useState('');
  const [showComprehensiveMode, setShowComprehensiveMode] = useState(false);

  // User preferences state
  const [preferredTechnologies, setPreferredTechnologies] = useState('');
  const [technicalConstraints, setTechnicalConstraints] = useState('');
  const [budgetConsiderations, setBudgetConsiderations] = useState('');
  const [timelineConstraints, setTimelineConstraints] = useState('');
  const [complianceRequirements, setComplianceRequirements] = useState('');
  const [integrationRequirements, setIntegrationRequirements] = useState('');
  const [scalabilityRequirements, setScalabilityRequirements] = useState('');
  const [securityRequirements, setSecurityRequirements] = useState('');
  const [additionalConsiderations, setAdditionalConsiderations] = useState('');

  const { toast } = useToast();
  const [amendmentInitialSuggestions, setAmendmentInitialSuggestions] = useState<ISuggestion[]>([]);
  const [appliedSuggestions, setAppliedSuggestions] = useState<ISuggestion[]>([]);

  // Handler to update both currentProposalText and comprehensiveProposal after amendment
  const handleAmendedProposalTextUpdate = (newText: string, isComprehensiveEdit: boolean = false) => {
    setCurrentProposalText(newText);
    // Only update comprehensiveProposal if this edit is intended for it
    // or if we're editing the comprehensive proposal directly
    if (isComprehensiveEdit || (comprehensiveProposal && currentProposalText === comprehensiveProposal)) {
      setComprehensiveProposal(newText);
    }
  };

  // Helper function to build user preferences object
  const buildUserPreferences = (): IUserPreferences | undefined => {
    const preferences: Partial<IUserPreferences> = {};

    if (preferredTechnologies.trim()) {
      preferences.preferredTechnologies = preferredTechnologies.split(',').map(tech => tech.trim()).filter(tech => tech);
    }
    if (technicalConstraints.trim()) {
      preferences.technicalConstraints = technicalConstraints.split(',').map(constraint => constraint.trim()).filter(constraint => constraint);
    }
    if (budgetConsiderations.trim()) {
      preferences.budgetConsiderations = budgetConsiderations.trim();
    }
    if (timelineConstraints.trim()) {
      preferences.timelineConstraints = timelineConstraints.trim();
    }
    if (complianceRequirements.trim()) {
      preferences.complianceRequirements = complianceRequirements.split(',').map(req => req.trim()).filter(req => req);
    }
    if (integrationRequirements.trim()) {
      preferences.integrationRequirements = integrationRequirements.split(',').map(req => req.trim()).filter(req => req);
    }
    if (scalabilityRequirements.trim()) {
      preferences.scalabilityRequirements = scalabilityRequirements.trim();
    }
    if (securityRequirements.trim()) {
      preferences.securityRequirements = securityRequirements.split(',').map(req => req.trim()).filter(req => req);
    }
    if (additionalConsiderations.trim()) {
      preferences.additionalConsiderations = additionalConsiderations.trim();
    }

    return Object.keys(preferences).length > 0 ? preferences as IUserPreferences : undefined;
  };

  const handleRfpTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setRfpContent(e.target.value);
    if (rfpPdfDataUri) {
      setRfpPdfDataUri(null);
      setUploadedFileName(null);
      toast({ title: "Input Switched", description: "Switched to manual text input. Uploaded PDF cleared." });
    }
  };

  useEffect(() => {
    if (solutionOverview) {
      // Prefill architectureInput only if it's empty, to preserve user edits
      setArchitectureInput(prev => prev.trim() ? prev : solutionOverview);
      
      // Initialize or update currentProposalText when solutionOverview changes
      setCurrentProposalText(prev => prev || solutionOverview);
    }
  }, [solutionOverview]);

  useEffect(() => {
    if (enhancementSuggestions && enhancementSuggestions.length > 0) {
      const mappedSuggestions: ISuggestion[] = enhancementSuggestions.map((text, index) => ({
        id: `enh-sug-${Date.now()}-${index}`,
        text,
        type: 'general' // Default type, can be refined if suggestion source provides more info
      }));
      setAmendmentInitialSuggestions(mappedSuggestions);
    } else {
      setAmendmentInitialSuggestions([]); // Clear if no suggestions
    }
  }, [enhancementSuggestions]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();

    reader.onload = (e) => {
      const result = e.target?.result as string;
      if (file.type === "application/pdf") {
        setRfpPdfDataUri(result);
        setRfpContent('');
        setUploadedFileName(file.name);
        toast({ title: "PDF Uploaded", description: `${file.name} ready for analysis.` });
      } else if (file.type === "text/plain" || file.type === "text/markdown") {
        setRfpContent(result);
        setRfpPdfDataUri(null);
        setUploadedFileName(file.name);
        toast({ title: "Text File Loaded", description: `Content from ${file.name} loaded into textarea.` });
      } else {
        setRfpContent('');
        setRfpPdfDataUri(null);
        setUploadedFileName(null);
        toast({
          title: "File Type Not Supported for Direct Parsing",
          description: `${file.name} selected. Please use .txt, .md, or .pdf. For other formats, copy/paste text.`,
          variant: "destructive",
          duration: 7000,
        });
      }
    };

    reader.onerror = () => {
      setRfpContent('');
      setRfpPdfDataUri(null);
      setUploadedFileName(null);
      toast({ title: "File Read Error", description: "Failed to read the file.", variant: "destructive" });
    };

    if (file.type === "application/pdf" || file.type === "text/plain" || file.type === "text/markdown") {
       if (file.type === "application/pdf") {
        reader.readAsDataURL(file);
      } else {
        reader.readAsText(file);
      }
    } else {
        setRfpContent('');
        setRfpPdfDataUri(null);
        setUploadedFileName(null);
        toast({
          title: "File Type Not Supported",
          description: `${file.name} selected. Please upload a .txt, .md, or .pdf file.`,
          variant: "destructive",
          duration: 7000,
        });
    }
    event.target.value = '';
  };

  const clearUploadedFile = () => {
    setRfpPdfDataUri(null);
    setUploadedFileName(null);
    toast({ title: "Upload Cleared", description: "Uploaded file has been removed." });
  };

  const handleAnalyzeRequirements = async () => {
    let analysisInput: AnalyzeRequirementsInput | null = null;

    if (rfpPdfDataUri) {
      analysisInput = { inputType: 'pdf', rfpPdfDataUri };
    } else if (rfpContent.trim()) {
      analysisInput = { inputType: 'text', rfpContent: rfpContent.trim() };
    }

    if (!analysisInput) {
      toast({ title: "Input Missing", description: "Please provide RFP content by typing or uploading a .txt, .md, or .pdf file.", variant: "destructive" });
      return;
    }

    setUnderstandingReqsAttempted(true);
    setIsLoadingRequirements(true);
    setUnderstandingReqs('');
    try {
      const result = await analyzeRequirementsAction(analysisInput);
      setUnderstandingReqs(result.understandingOfTheRequirements);
      toast({ title: "Success", description: "Requirements analyzed." });
    } catch (error: any) {
      toast({ title: "Error Analyzing Requirements", description: error.message || "Failed to analyze requirements.", variant: "destructive" });
    } finally {
      setIsLoadingRequirements(false);
    }
  };

  const handleSynthesizeSolution = async () => {
    if (!solutionKeyPoints.trim()) {
      toast({ title: "Input Missing", description: "Please provide key RFP requirements for the solution.", variant: "destructive" });
      return;
    }
    setSolutionOverviewAttempted(true);
    setIsLoadingSolution(true);
    setSolutionOverview('');
    setOemSolutions([]);
    try {
      // First try without fallback mode
      const result = await generateSolutionOverviewAction({ 
        rfpRequirements: solutionKeyPoints,
        _fallbackMode: false
      });
      setSolutionOverview(result.solutionOverview);
      setOemSolutions(result.suggestedOemSolutions || []);
      toast({ title: "Success", description: "Solution overview generated." });
    } catch (error: any) {
      logServerError(error, 'Error in solution overview generation');
      
      // If first attempt fails, try with fallback mode
      try {
        toast({ 
          title: "Retrying with fallback mode", 
          description: "Encountered an issue with web research, falling back to local knowledge." 
        });
        
        const fallbackResult = await generateSolutionOverviewAction({ 
          rfpRequirements: solutionKeyPoints,
          _fallbackMode: true
        });
        
        setSolutionOverview(fallbackResult.solutionOverview);
        setOemSolutions(fallbackResult.suggestedOemSolutions || []);
        toast({ 
          title: "Success", 
          description: "Solution overview generated using fallback mode (no web research)." 
        });
      } catch (fallbackError: any) {
        logServerError(fallbackError, 'Fallback solution generation failed');
        toast({ 
          title: "Error", 
          description: fallbackError.message || "Failed to generate solution overview in fallback mode.", 
          variant: "destructive" 
        });
      }
    } finally {
      setIsLoadingSolution(false);
    }
  };

  const handleEnhanceProposal = async () => {
    if (!understandingReqs.trim() && !solutionOverview.trim()) {
      toast({ title: "Input Missing", description: "Please generate 'Understanding Requirements' and 'Solution Overview' sections first.", variant: "destructive" });
      return;
    }
    setIsLoadingEnhancements(true);
    setEnhancementSuggestions([]);
    try {
      const result = await enhanceProposalAction({
        understandingRequirements: understandingReqs,
        solutionOverview: solutionOverview,
      });
      setEnhancementSuggestions(result.recommendations || []);
      toast({ title: "Success", description: "Enhancement suggestions provided." });
    } catch (error: any) {
      toast({ title: "Error", description: error.message || "Failed to get enhancement suggestions.", variant: "destructive" });
    } finally {
      setIsLoadingEnhancements(false);
    }
  };

  const handleGenerateDiagram = async () => {
    if (!architectureInput.trim()) {
      toast({ title: "Input Missing", description: "Please provide a description for the architecture diagram.", variant: "destructive" });
      return;
    }
    setIsLoadingDiagram(true);
    try {
      const result = await generateArchitectureDiagramAction({
        architectureDescription: architectureInput,
        diagramType: diagramType,
      });
      setMermaidScript(result.mermaidScript);
      toast({ title: "Success", description: "Architecture diagram generated." });
    } catch (error: any) {
      toast({ title: "Error Generating Diagram", description: error.message || "Failed to generate diagram.", variant: "destructive" });
    } finally {
      setIsLoadingDiagram(false);
    }
  };

  const handleGenerateComprehensiveProposal = async () => {
    let analysisInput: AnalyzeRequirementsInput | null = null;

    if (rfpPdfDataUri) {
      analysisInput = { inputType: 'pdf', rfpPdfDataUri };
    } else if (rfpContent.trim()) {
      analysisInput = { inputType: 'text', rfpContent: rfpContent.trim() };
    }

    if (!analysisInput) {
      toast({ title: "Input Missing", description: "Please provide RFP content by typing or uploading a .txt, .md, or .pdf file.", variant: "destructive" });
      return;
    }

    setIsLoadingComprehensive(true);
    setComprehensiveProgress(0);
    setComprehensiveCurrentStep('Initializing...');
    setComprehensiveProposal('');
    setTechnicalImplementation('');

    // Simulate progress updates
    let progressInterval: ReturnType<typeof setInterval>;
    let stepInterval: ReturnType<typeof setInterval>;

    progressInterval = setInterval(() => {
      setComprehensiveProgress(prev => {
        if (prev < 90) {
          const increment = Math.random() * 10 + 5; // Random increment between 5-15
          return Math.min(prev + increment, 90);
        }
        return prev;
      });
    }, 1000);

    // Update step names based on progress
    stepInterval = setInterval(() => {
      setComprehensiveProgress(current => {
        if (current < 30) {
          setComprehensiveCurrentStep('Analyzing Requirements...');
        } else if (current < 60) {
          setComprehensiveCurrentStep('Generating Solution Overview...');
        } else if (current < 90) {
          setComprehensiveCurrentStep('Creating Technical Implementation...');
        } else {
          setComprehensiveCurrentStep('Finalizing Proposal...');
        }
        return current;
      });
    }, 2000);

    try {
      const userPreferences = buildUserPreferences();
      const result = await generateComprehensiveProposalAction({
        inputType: analysisInput.inputType,
        rfpContent: analysisInput.inputType === 'text' ? analysisInput.rfpContent : undefined,
        rfpPdfDataUri: analysisInput.inputType === 'pdf' ? analysisInput.rfpPdfDataUri : undefined,
        generateRequirements: true,
        generateSolution: true,
        generateTechnical: true,
        userPreferences,
      });

      // Clear intervals and set to 100%
      clearInterval(progressInterval);
      clearInterval(stepInterval);
      setComprehensiveProgress(100);
      setComprehensiveCurrentStep('Complete!');

      // Update individual sections for compatibility with existing UI
      if (result.requirementsAnalysis) {
        setUnderstandingReqs(result.requirementsAnalysis.understandingOfTheRequirements);
        setUnderstandingReqsAttempted(true);
      }

      if (result.solutionOverview) {
        setSolutionOverview(result.solutionOverview.solutionOverview);
        setOemSolutions(result.solutionOverview.suggestedOemSolutions || []);
        setSolutionOverviewAttempted(true);
      }

      if (result.technicalImplementation) {
        setTechnicalImplementation(result.technicalImplementation.technicalImplementation);
      }

      setComprehensiveProposal(result.completeProposal);

      toast({
        title: "Success",
        description: `Comprehensive proposal generated! ${result.generationMetadata.estimatedWordCount} words across ${result.generationMetadata.generatedSections} sections in ${result.generationMetadata.generationTime}s.`
      });

    } catch (error: any) {
      // Clear intervals on error
      clearInterval(progressInterval);
      clearInterval(stepInterval);
      toast({ title: "Error", description: error.message || "Failed to generate comprehensive proposal.", variant: "destructive" });
    } finally {
      setIsLoadingComprehensive(false);
      setComprehensiveProgress(0);
      setComprehensiveCurrentStep('');
    }
  };

  const handleExportProposal = () => {
    // Use comprehensive proposal if available, otherwise fall back to individual sections
    let markdownContent = '';

    // Build diagram section once to reuse in both branches
    let diagramSection = '';
    if (mermaidSvg) {
      try {
        // Convert SVG content to data URL for proper markdown image embedding
        const svgDataUrl = `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(mermaidSvg)))}`;
        diagramSection = `
## Architecture Diagram

![Architecture Diagram](${svgDataUrl})
`;
      } catch (error) {
        console.error('Error converting SVG to data URL:', error);
        // Fallback to mermaid script if SVG conversion fails
        diagramSection = `
## Architecture Diagram

\`\`\`mermaid
${mermaidScript}
\`\`\`
`;
        toast({
          title: 'SVG Export Warning',
          description: 'Could not export diagram as image. Using Mermaid script instead.',
          variant: 'destructive',
        });
      }
    } else if (mermaidScript) {
      diagramSection = `
## Architecture Diagram

\`\`\`mermaid
${mermaidScript}
\`\`\`
`;
    }

    if (comprehensiveProposal) {
      // Append diagram section to the comprehensive proposal
      markdownContent = `${comprehensiveProposal.trim()}
${diagramSection}`;
    } else {
      // Use the currentProposalText if available, otherwise fall back to solutionOverview
      const proposalTextForDraft = currentProposalText || solutionOverview;
      markdownContent = buildDraftProposalMarkdown(
        understandingReqs,
        proposalTextForDraft,
        oemSolutions,
        technicalImplementation,
        diagramSection,
        appliedSuggestions,
        enhancementSuggestions
      );
    }

    const blob = new Blob([markdownContent.trim()], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ProposalPilot_Comprehensive.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Show appropriate success message based on what was exported
    const description = comprehensiveProposal
      ? "Comprehensive proposal downloaded as Markdown."
      : "Proposal draft downloaded as Markdown.";
    toast({ title: "Exported", description });
  };


  return (
    <div className="min-h-screen bg-background">
      <AppHeader />
      <main className="container mx-auto px-4 py-8 md:p-8 space-y-8 flex-grow">

        <SectionCard
          title="1. Input RFP Content"
          description="Paste RFP text, or upload a .txt, .md, or .pdf file for analysis."
          icon={FileText}
          actionButton={
            <Button
              onClick={handleAnalyzeRequirements}
              disabled={isLoadingRequirements || (!rfpContent.trim() && !rfpPdfDataUri)}
            >
              {isLoadingRequirements ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Lightbulb className="mr-2 h-4 w-4" />}
              Analyze Requirements
            </Button>
          }
        >
          <div className="space-y-2">
            <Label htmlFor="rfpContent" className="sr-only">RFP Content Area</Label>
            <Textarea
              id="rfpContent"
              value={rfpContent}
              onChange={handleRfpTextChange}
              placeholder={rfpPdfDataUri ? "PDF uploaded. Text input disabled." : "Paste RFP content here, or use 'Upload File' for .txt, .md, .pdf."}
              className="min-h-[200px] text-sm"
              aria-label="RFP Content Input"
              disabled={!!rfpPdfDataUri}
            />
            <div className="pt-2 flex items-center gap-2">
              <Label
                htmlFor="rfpFileUpload"
                className={cn(
                  buttonVariants({ variant: "outline" }),
                  "cursor-pointer"
                )}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') (document.getElementById('rfpFileUpload') as HTMLInputElement)?.click(); }}
              >
                <UploadCloud className="mr-2 h-4 w-4" />
                Upload File (.txt, .md, .pdf)
              </Label>
              <Input
                id="rfpFileUpload"
                type="file"
                accept=".txt,.md,.pdf,text/plain,text/markdown,application/pdf"
                onChange={handleFileUpload}
                className="hidden"
              />
              {uploadedFileName && (
                <div className="flex items-center gap-2 text-sm p-2 border rounded-md bg-muted/50">
                  <FileUp className="h-4 w-4 text-green-600" />
                  <span>{uploadedFileName}</span>
                  <Button variant="ghost" size="icon" onClick={clearUploadedFile} className="h-6 w-6">
                    <CircleX className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                  </Button>
                </div>
              )}
            </div>
             {rfpPdfDataUri && (
                <p className="text-xs text-muted-foreground mt-1">
                    Note: If you type in the text area above, the uploaded PDF will be cleared.
                </p>
            )}
          </div>
        </SectionCard>

        {/* User Preferences Section */}
        <SectionCard
          title="1.5. Technology Preferences & Considerations"
          description="Specify your preferred technologies, constraints, and other considerations to guide the AI agents in generating tailored proposals."
          icon={KeyRound}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="preferredTechnologies">Preferred Technologies</Label>
                <Textarea
                  id="preferredTechnologies"
                  value={preferredTechnologies}
                  onChange={(e) => setPreferredTechnologies(e.target.value)}
                  placeholder="e.g., Preferred Platforms, Cloud Services, Frameworks (comma-separated)"
                  className="min-h-[80px] text-sm"
                />
              </div>

              <div>
                <Label htmlFor="technicalConstraints">Technical Constraints</Label>
                <Textarea
                  id="technicalConstraints"
                  value={technicalConstraints}
                  onChange={(e) => setTechnicalConstraints(e.target.value)}
                  placeholder="e.g., Must use on-premise infrastructure, No cloud services (comma-separated)"
                  className="min-h-[80px] text-sm"
                />
              </div>

              <div>
                <Label htmlFor="budgetConsiderations">Budget Considerations</Label>
                <Textarea
                  id="budgetConsiderations"
                  value={budgetConsiderations}
                  onChange={(e) => setBudgetConsiderations(e.target.value)}
                  placeholder="e.g., Limited budget, prefer open-source solutions, cost-effective approach"
                  className="min-h-[60px] text-sm"
                />
              </div>

              <div>
                <Label htmlFor="timelineConstraints">Timeline Constraints</Label>
                <Textarea
                  id="timelineConstraints"
                  value={timelineConstraints}
                  onChange={(e) => setTimelineConstraints(e.target.value)}
                  placeholder="e.g., Must deliver within 6 months, phased delivery required"
                  className="min-h-[60px] text-sm"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="complianceRequirements">Compliance Requirements</Label>
                <Textarea
                  id="complianceRequirements"
                  value={complianceRequirements}
                  onChange={(e) => setComplianceRequirements(e.target.value)}
                  placeholder="e.g., GDPR, HIPAA, SOX, ISO 27001 (comma-separated)"
                  className="min-h-[80px] text-sm"
                />
              </div>

              <div>
                <Label htmlFor="integrationRequirements">Integration Requirements</Label>
                <Textarea
                  id="integrationRequirements"
                  value={integrationRequirements}
                  onChange={(e) => setIntegrationRequirements(e.target.value)}
                  placeholder="e.g., SAP ERP, Salesforce, Active Directory (comma-separated)"
                  className="min-h-[80px] text-sm"
                />
              </div>

              <div>
                <Label htmlFor="scalabilityRequirements">Scalability Requirements</Label>
                <Textarea
                  id="scalabilityRequirements"
                  value={scalabilityRequirements}
                  onChange={(e) => setScalabilityRequirements(e.target.value)}
                  placeholder="e.g., Support 10,000 concurrent users, auto-scaling capabilities"
                  className="min-h-[60px] text-sm"
                />
              </div>

              <div>
                <Label htmlFor="securityRequirements">Security Requirements</Label>
                <Textarea
                  id="securityRequirements"
                  value={securityRequirements}
                  onChange={(e) => setSecurityRequirements(e.target.value)}
                  placeholder="e.g., Multi-factor authentication, encryption at rest, penetration testing (comma-separated)"
                  className="min-h-[60px] text-sm"
                />
              </div>
            </div>
          </div>

          <div className="mt-4">
            <Label htmlFor="additionalConsiderations">Additional Considerations</Label>
            <Textarea
              id="additionalConsiderations"
              value={additionalConsiderations}
              onChange={(e) => setAdditionalConsiderations(e.target.value)}
              placeholder="Any other specific requirements, preferences, or constraints that should guide the proposal generation..."
              className="min-h-[80px] text-sm"
            />
          </div>

          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-800">
              <strong>💡 Tip:</strong> These preferences will be considered by all AI agents when generating your proposal.
              For technologies and constraints, use comma-separated values. Leave fields empty if not applicable.
            </p>
          </div>
        </SectionCard>

        {/* Comprehensive Proposal Generation Section */}
        <SectionCard
          title="🚀 Generate Comprehensive Proposal"
          description="Use AI agents to generate a complete technical proposal with Requirements Analysis, Solution Overview, and Technical Implementation sections."
          icon={Zap}
          isLoading={isLoadingComprehensive}
          actionButton={
            <div className="flex flex-col sm:flex-row gap-2 w-full">
              <Button
                onClick={handleGenerateComprehensiveProposal}
                disabled={isLoadingComprehensive || (!rfpContent.trim() && !rfpPdfDataUri)}
                className="flex-1"
              >
                {isLoadingComprehensive ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {comprehensiveCurrentStep}
                  </>
                ) : (
                  <>
                    <Zap className="mr-2 h-4 w-4" />
                    Generate Complete Proposal
                  </>
                )}
              </Button>
              {comprehensiveProposal && (
                <Button
                  variant="outline"
                  onClick={() => setShowComprehensiveMode(!showComprehensiveMode)}
                  className="flex-shrink-0"
                >
                  {showComprehensiveMode ? 'Hide' : 'Show'} Preview
                </Button>
              )}
            </div>
          }
        >
          {isLoadingComprehensive && (
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">{comprehensiveCurrentStep}</span>
                <span className="font-medium">{comprehensiveProgress}%</span>
              </div>
              <div className="w-full bg-secondary rounded-full h-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${comprehensiveProgress}%` }}
                />
              </div>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className={`flex items-center gap-1 ${comprehensiveProgress >= 33 ? 'text-green-600' : 'text-muted-foreground'}`}>
                  {comprehensiveProgress >= 33 ? <CheckCircle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
                  Requirements
                </div>
                <div className={`flex items-center gap-1 ${comprehensiveProgress >= 66 ? 'text-green-600' : 'text-muted-foreground'}`}>
                  {comprehensiveProgress >= 66 ? <CheckCircle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
                  Solution
                </div>
                <div className={`flex items-center gap-1 ${comprehensiveProgress >= 90 ? 'text-green-600' : 'text-muted-foreground'}`}>
                  {comprehensiveProgress >= 90 ? <CheckCircle className="h-3 w-3" /> : <Clock className="h-3 w-3" />}
                  Technical
                </div>
              </div>
            </div>
          )}

          {!isLoadingComprehensive && !comprehensiveProposal && (
            <div className="text-center py-8 text-muted-foreground">
              <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">Ready to Generate Comprehensive Proposal</p>
              <p className="text-sm">
                This will use multiple AI agents to create detailed sections:<br />
                • Requirements Analysis Agent (enhanced)<br />
                • Solution Overview Agent (enhanced)<br />
                • Technical Implementation Agent (new)
              </p>
            </div>
          )}

          {comprehensiveProposal && showComprehensiveMode && (
            <div className="mt-4">
              <Label htmlFor="comprehensivePreview">Complete Proposal Preview</Label>
              <Textarea
                id="comprehensivePreview"
                value={comprehensiveProposal}
                onChange={(e) => setComprehensiveProposal(e.target.value)}
                placeholder="Comprehensive proposal will appear here..."
                className="min-h-[400px] text-sm bg-white font-mono"
                aria-label="Comprehensive Proposal Preview"
              />
            </div>
          )}

          {technicalImplementation && !showComprehensiveMode && (
            <div className="mt-4">
              <Label htmlFor="technicalImplementationPreview">Technical Implementation Section</Label>
              <Textarea
                id="technicalImplementationPreview"
                value={technicalImplementation}
                onChange={(e) => setTechnicalImplementation(e.target.value)}
                placeholder="Technical implementation details will appear here..."
                className="min-h-[300px] text-sm bg-white"
                aria-label="Technical Implementation Preview"
              />
            </div>
          )}
        </SectionCard>

        <Separator className="my-8" />

        {(understandingReqsAttempted) && (
          <SectionCard
            title="2. Understanding the Requirements"
            description="AI-generated summary of the RFP requirements. You can edit this text."
            icon={Lightbulb}
            isLoading={isLoadingRequirements}
          >
            {isLoadingRequirements && !understandingReqs && <div className="text-muted-foreground">Analyzing requirements...</div>}
            <Textarea
              id="understandingReqs"
              value={understandingReqs}
              onChange={(e) => setUnderstandingReqs(e.target.value)}
              placeholder="Generated understanding of requirements will appear here..."
              className="min-h-[200px] text-sm bg-white"
              aria-label="Understanding the Requirements Output"
              readOnly={isLoadingRequirements}
            />
          </SectionCard>
        )}

        <Separator className="my-8" />

        <SectionCard
          title="3. Define Solution Scope"
          description="Provide key requirements or focus areas from the RFP to guide solution generation. AI will use real web search for current insights."
          icon={KeyRound}
          actionButton={
             <Button onClick={handleSynthesizeSolution} disabled={isLoadingSolution || !solutionKeyPoints.trim()}>
              {isLoadingSolution ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Combine className="mr-2 h-4 w-4" />}
              Synthesize Solution Overview
            </Button>
          }
        >
          <Label htmlFor="solutionKeyPoints" className="sr-only">Key RFP Requirements for Solution</Label>
          <Textarea
            id="solutionKeyPoints"
            value={solutionKeyPoints}
            onChange={(e) => setSolutionKeyPoints(e.target.value)}
            placeholder="E.g., 'Cloud-native architecture, scalability for 1 million users, data security compliance with ISO 27001...'"
            className="min-h-[150px] text-sm"
            aria-label="Key RFP Requirements for Solution Input"
          />
        </SectionCard>

         {(solutionOverviewAttempted) && (
          <SectionCard
            title="4. Solution Overview"
            description="AI-generated solution overview and suggested OEM solutions. Editable text."
            icon={Combine}
            isLoading={isLoadingSolution}
          >
            {isLoadingSolution && !solutionOverview && <div className="text-muted-foreground">Generating solution overview...</div>}
            <Label htmlFor="solutionOverviewText">Solution Overview Details</Label>
            <Textarea
              id="solutionOverviewText"
              value={solutionOverview}
              onChange={(e) => setSolutionOverview(e.target.value)}
              placeholder="Generated solution overview will appear here..."
              className="min-h-[250px] text-sm bg-white"
              aria-label="Solution Overview Output"
              readOnly={isLoadingSolution}
            />
            {oemSolutions.length > 0 && (
              <div className="mt-4">
                <h4 className="font-semibold mb-2 text-sm">Suggested OEM Solutions:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {oemSolutions.map((oem, index) => <li key={index}>{oem}</li>)}
                </ul>
              </div>
            )}
             {!isLoadingSolution && !solutionOverview && oemSolutions.length === 0 && <p className="text-muted-foreground text-sm">No solution overview generated yet.</p>}
          </SectionCard>
        )}

        <Separator className="my-8" />

        <SectionCard
          title="5. Architecture Diagram"
          description="Visualize your solution. Use AI to generate a starting point or craft it manually."
          icon={Network}
        >
          <div className="space-y-4">
            <div>
              <Label htmlFor="architectureInput">Architecture Description (for AI generation)</Label>
              <Textarea
                id="architectureInput"
                value={architectureInput}
                onChange={(e) => setArchitectureInput(e.target.value)}
                placeholder="Describe the system, its components, interactions, and data flow. This will be used by the AI to generate a diagram. You can use the Solution Overview above as a starting point."
                className="min-h-[100px] text-sm"
                aria-label="Architecture description for AI generation"
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-4 items-center">
                <div className="w-full sm:w-1/2">
                    <Label htmlFor="diagramTypeSelect">Diagram Type</Label>
                    <Select
                        value={diagramType}
                        onValueChange={(value: 'conceptual' | 'reference') => setDiagramType(value)}
                    >
                        <SelectTrigger id="diagramTypeSelect" className="w-full">
                            <SelectValue placeholder="Select diagram type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="conceptual">Conceptual Diagram</SelectItem>
                            <SelectItem value="reference">Reference Architecture</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                <Button
                  onClick={handleGenerateDiagram}
                  disabled={isLoadingDiagram || !architectureInput.trim()}
                  className="w-full sm:w-auto mt-4 sm:mt-0 sm:self-end" // Adjusted for better alignment
                >
                    {isLoadingDiagram ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
                    Generate Diagram with AI
                </Button>
            </div>
          </div>

          <Separator className="my-6" />

          <MermaidPreview
            initialScript={mermaidScript}
            onScriptChange={setMermaidScript}
            onSvgChange={setMermaidSvg}
            title="Diagram Editor & Preview"
            description="Edit the Mermaid script directly or refine the AI-generated version."
          />
        </SectionCard>

        <Separator className="my-8" />

        <SectionCard
          title="6. Enhance Your Proposal"
          description="Get AI-powered suggestions to improve your generated content."
          icon={Sparkles}
          isLoading={isLoadingEnhancements}
           actionButton={
            <Button onClick={handleEnhanceProposal} disabled={isLoadingEnhancements || (!understandingReqs && !solutionOverview)}>
              {isLoadingEnhancements ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
              Get Enhancement Suggestions
            </Button>
          }
        >
          {isLoadingEnhancements && enhancementSuggestions.length === 0 && <div className="text-muted-foreground">Fetching suggestions...</div>}
          {enhancementSuggestions.length > 0 ? (
            <div className="space-y-3">
              <h4 className="font-semibold text-md">Recommendations:</h4>
              <ul className="list-disc list-inside space-y-2 text-sm bg-accent/10 p-4 rounded-md border border-accent">
                {enhancementSuggestions.map((suggestion, index) => (
                  <li key={index} className="text-accent-foreground_dark">{suggestion}</li>
                ))}
              </ul>
            </div>
          ) : (
            !isLoadingEnhancements && <p className="text-muted-foreground text-sm">Click the button above to get suggestions.</p>
          )}
          {(comprehensiveProposal || solutionOverview) && amendmentInitialSuggestions.length > 0 && (
            <div className="mt-6 pt-6 border-t border-border">
              <h3 className="text-lg font-semibold mb-4">Refine with Suggestions & Rating</h3>
              <ProposalAmendmentSection
                initialProposalText={comprehensiveProposal || solutionOverview || understandingReqs || 'No proposal content available.'}
                initialSuggestions={amendmentInitialSuggestions}
                appliedSuggestions={appliedSuggestions}
                onAppliedSuggestionsChange={setAppliedSuggestions}
                onProposalTextChange={(newText) => handleAmendedProposalTextUpdate(newText, !!comprehensiveProposal && comprehensiveProposal === (comprehensiveProposal || solutionOverview || understandingReqs || 'No proposal content available.'))}
              />
            </div>
          )}
        </SectionCard>

        <Separator className="my-8" />

        <Card className="shadow-lg">
          <CardHeader>
             <div className="flex items-center space-x-3">
                <Download className="w-7 h-7 text-primary" />
                <div>
                    <CardTitle className="text-xl">7. Export Proposal</CardTitle>
                    <CardDescription>Download the complete proposal draft as a Markdown file.</CardDescription>
                </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              This will compile all generated and edited sections into a single .md file. If a diagram is available, it will be embedded as an SVG image; otherwise, the Mermaid script will be included.
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleExportProposal} className="w-full md:w-auto">
              <Download className="mr-2 h-4 w-4" /> Export as Markdown
            </Button>
          </CardFooter>
        </Card>

      </main>
      <footer className="text-center p-4 text-sm text-muted-foreground border-t">
        ProposalPilot &copy; {new Date().getFullYear()}
      </footer>
    </div>
  );
}
