// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node', // Suitable for backend/service unit tests
  moduleNameMapper: {
    // Handle module path aliases
    // This ensures <PERSON><PERSON> can resolve imports starting with '@/'
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  // Automatically clear mock calls, instances, contexts and results before every test
  clearMocks: true,
  // The directory where Jest should output its coverage files
  coverageDirectory: 'coverage',
  // A list of paths to directories that <PERSON><PERSON> should use to search for files in
  roots: ['<rootDir>/src'],
  // The pattern or patterns <PERSON><PERSON> uses to detect test files
  testMatch: [
    '**/__tests__/**/*.+(ts|tsx|js)',
    '**/?(*.)+(spec|test).+(ts|tsx|js)',
  ],
  // An array of regexp pattern strings that are matched against all test paths, matched tests are skipped
  testPathIgnorePatterns: [
    '/node_modules/',
    '/.next/',
    '/public/',
    '/helm-charts/', // Assuming helm charts are not part of testable code
    '/proposalpilot-chart/' // From memory
  ],
  // An array of regexp pattern strings that are matched against all source file paths, matched files will skip transformation
  transformIgnorePatterns: [
    '/node_modules/',
    '^.+\.module\.(css|sass|scss)$', // Ignore CSS modules if any
  ],
  // Specifies which files to include in the coverage report
  collectCoverageFrom: [
    'src/services/**/*.{ts,tsx}', // Focus on services for now
    '!src/**/*.d.ts',           // Exclude type definition files
    '!src/**/index.ts',         // Exclude barrel files if they only re-export
    // Add '!src/app/**' or '!src/pages/**' if you want to exclude Next.js specific dirs from service coverage
  ],
  // Optional: Enforce coverage thresholds. Project rules state "Minimum 80% for new code".
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  // Indicates whether each individual test should be reported during the run
  verbose: true,
};
