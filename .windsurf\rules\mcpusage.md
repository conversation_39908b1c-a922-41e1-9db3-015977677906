---
trigger: always_on
---

The following MCPs are available to you as tools and function when required:

# Sequential Thinking MCP
- Use Sequential Thinking MCP primarily for debugging, troubleshooting, complex problem-solving, and detailed project planning.
- Trigger recursive calls judiciously—only when new progress or significant information is expected to avoid performance degradation and 
  infinite loops.

# GitHub MCP
- Use the GitHub MCP server to for ALL github actions.
- Ensure commit messages are clear, descriptive, and incremental to maintain a clean project history.
- Never overwrite or unintentionally modify critical files such as README.md or other documentation without explicit user approval.

# Prisma MCP
- Use prisma-mcp-server for all database schema management, migrations, and ORM-related operations.
- Validate schema changes locally before applying migrations to production environments.

# PostgreSQL MCP
- Use postgresql MCP for all database interactions, queries, and connection management.
- Ensure secure handling of credentials and use connection pooling where applicable for performance.

# Context7 MCP
- Use context7-mcp for managing application context, session state, and user-specific data.
- Ensure context updates are atomic and consistent to prevent race conditions.

# Browser-Tools MCP
- Use browser-tools for all browser automation, scraping, and UI interaction tasks.
- Handle errors gracefully and implement retries for flaky network or UI conditions.